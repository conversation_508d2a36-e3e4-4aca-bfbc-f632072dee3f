#!/usr/bin/env python3
"""
修复数据集元信息的脚本
主要解决转移矩阵导致生成无效分子的问题
"""

import os
import sys
import json
import pandas as pd

# 添加当前目录到Python路径
sys.path.append('.')
sys.path.append('./graph_dit')

try:
    from graph_dit.datasets.dataset import compute_meta
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装所需依赖: torch, torch_geometric, rdkit")
    sys.exit(1)

def fix_dataset_meta(dataset_name="fgfr1_with_toxic_solubility"):
    """
    重新生成数据集的元信息，修复转移矩阵问题
    """
    print(f"开始修复数据集 {dataset_name} 的元信息...")
    
    # 数据路径
    data_dir = "data"
    raw_dir = os.path.join(data_dir, "raw")
    csv_path = os.path.join(raw_dir, f"{dataset_name}.csv")
    meta_path = os.path.join(raw_dir, f"{dataset_name}.meta.json")
    
    # 检查文件是否存在
    if not os.path.exists(csv_path):
        print(f"错误: 找不到数据文件 {csv_path}")
        return False
    
    print(f"读取数据文件: {csv_path}")
    df = pd.read_csv(csv_path)
    print(f"数据集包含 {len(df)} 个分子")
    
    # 创建训练/测试分割（使用前80%作为训练集）
    total_size = len(df)
    train_size = int(0.8 * total_size)
    
    train_index = list(range(train_size))
    test_index = list(range(train_size, total_size))
    
    print(f"训练集: {len(train_index)} 个分子")
    print(f"测试集: {len(test_index)} 个分子")
    
    # 重新计算元信息
    print("重新计算元信息...")
    try:
        meta_dict = compute_meta(
            root=raw_dir,
            source_name=dataset_name,
            train_index=train_index,
            test_index=test_index
        )
        
        # 保存新的元信息
        backup_path = meta_path + ".backup"
        if os.path.exists(meta_path):
            print(f"备份原始元信息到: {backup_path}")
            os.rename(meta_path, backup_path)
        
        print(f"保存新的元信息到: {meta_path}")
        with open(meta_path, 'w') as f:
            json.dump(meta_dict, f, indent=2)
        
        # 验证转移矩阵
        print("\n验证转移矩阵...")
        transition_E = meta_dict['transition_E']
        
        # 统计有多少原子对有非零的键转移概率
        non_zero_bonds = 0
        total_pairs = 0
        
        for i in range(len(transition_E)):
            for j in range(len(transition_E[i])):
                total_pairs += 1
                bond_prob = sum(transition_E[i][j][1:])  # 除了无键之外的概率
                if bond_prob > 0.01:
                    non_zero_bonds += 1
        
        print(f"总原子对数: {total_pairs}")
        print(f"有键连接的原子对数: {non_zero_bonds}")
        print(f"有键连接比例: {non_zero_bonds/total_pairs*100:.2f}%")
        
        # 检查键类型分布
        bond_dist = meta_dict['bond_type_dist']
        print(f"\n键类型分布:")
        bond_names = ['无键', '单键', '双键', '三键', '芳香键']
        for i, (name, count) in enumerate(zip(bond_names, bond_dist)):
            if i == 0:
                continue  # 跳过无键
            total_bonds = sum(bond_dist[1:])
            if total_bonds > 0:
                percentage = count / total_bonds * 100
                print(f"  {name}: {count} ({percentage:.2f}%)")
        
        print(f"\n✅ 数据集 {dataset_name} 的元信息修复完成!")
        return True
        
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    dataset_name = "fgfr1_with_toxic_solubility"
    
    if len(sys.argv) > 1:
        dataset_name = sys.argv[1]
    
    print("=" * 60)
    print("Graph-DiT 数据集元信息修复工具")
    print("=" * 60)
    
    success = fix_dataset_meta(dataset_name)
    
    if success:
        print("\n🎉 修复完成! 现在可以重新训练模型了。")
        print("\n建议步骤:")
        print("1. 删除已处理的数据缓存: rm -rf data/processed/")
        print("2. 重新开始训练")
    else:
        print("\n❌ 修复失败，请检查错误信息。")

if __name__ == "__main__":
    main()
