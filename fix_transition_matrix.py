#!/usr/bin/env python3
"""
直接修复转移矩阵的脚本
不依赖复杂的库，直接操作JSON文件
"""

import json
import os
import numpy as np

def fix_transition_matrix(meta_file_path):
    """
    修复转移矩阵，解决生成无效分子的问题
    """
    print(f"读取元信息文件: {meta_file_path}")
    
    # 备份原文件
    backup_path = meta_file_path + ".backup"
    if not os.path.exists(backup_path):
        print(f"备份原文件到: {backup_path}")
        with open(meta_file_path, 'r') as f:
            content = f.read()
        with open(backup_path, 'w') as f:
            f.write(content)
    
    # 读取元信息
    with open(meta_file_path, 'r') as f:
        meta_dict = json.load(f)
    
    print("原始转移矩阵统计:")
    transition_E = np.array(meta_dict['transition_E'])
    print(f"转移矩阵形状: {transition_E.shape}")
    
    # 统计原始情况
    total_pairs = 0
    no_bond_dominant = 0
    has_bonds = 0
    
    for i in range(len(transition_E)):
        for j in range(len(transition_E[i])):
            total_pairs += 1
            no_bond_prob = transition_E[i][j][0]  # 无键概率
            bond_prob = sum(transition_E[i][j][1:])  # 有键概率
            
            if no_bond_prob > 0.9:
                no_bond_dominant += 1
            if bond_prob > 0.01:
                has_bonds += 1
    
    print(f"总原子对数: {total_pairs}")
    print(f"无键概率>90%的对数: {no_bond_dominant} ({no_bond_dominant/total_pairs*100:.1f}%)")
    print(f"有键概率>1%的对数: {has_bonds} ({has_bonds/total_pairs*100:.1f}%)")
    
    # 修复转移矩阵
    print("\n开始修复转移矩阵...")
    
    # 获取活跃原子的索引
    active_atoms = meta_dict['active_atoms']
    atom_to_index = {}
    
    # 常见原子的原子序数
    atom_numbers = {'C': 6, 'N': 7, 'O': 8, 'F': 9, 'P': 15, 'S': 16, 'Cl': 17, 'Br': 35, 'I': 53}
    
    for atom in active_atoms:
        if atom in atom_numbers:
            # 转移矩阵的索引是原子序数-2
            atom_to_index[atom] = atom_numbers[atom] - 2
    
    print(f"活跃原子: {active_atoms}")
    print(f"原子索引映射: {atom_to_index}")
    
    # 修复策略：对于常见的化学键，设置合理的转移概率
    fixed_pairs = 0
    
    # 常见的化学键类型和它们的合理概率分布
    # [无键, 单键, 双键, 三键, 芳香键]
    bond_patterns = {
        ('C', 'C'): [0.3, 0.5, 0.15, 0.02, 0.03],  # C-C键
        ('C', 'N'): [0.4, 0.45, 0.1, 0.02, 0.03],   # C-N键
        ('C', 'O'): [0.5, 0.4, 0.08, 0.0, 0.02],    # C-O键
        ('C', 'F'): [0.7, 0.3, 0.0, 0.0, 0.0],      # C-F键
        ('C', 'Cl'): [0.7, 0.3, 0.0, 0.0, 0.0],     # C-Cl键
        ('C', 'Br'): [0.8, 0.2, 0.0, 0.0, 0.0],     # C-Br键
        ('C', 'S'): [0.6, 0.35, 0.05, 0.0, 0.0],    # C-S键
        ('N', 'N'): [0.6, 0.25, 0.1, 0.05, 0.0],    # N-N键
        ('N', 'O'): [0.7, 0.25, 0.05, 0.0, 0.0],    # N-O键
        ('O', 'O'): [0.8, 0.15, 0.05, 0.0, 0.0],    # O-O键
    }
    
    for (atom1, atom2), probs in bond_patterns.items():
        if atom1 in atom_to_index and atom2 in atom_to_index:
            i = atom_to_index[atom1]
            j = atom_to_index[atom2]
            
            # 设置双向转移概率
            transition_E[i][j] = probs
            transition_E[j][i] = probs
            fixed_pairs += 2
            
            print(f"修复 {atom1}-{atom2} 键: {probs}")
    
    print(f"修复了 {fixed_pairs} 个原子对的转移概率")
    
    # 更新元信息
    meta_dict['transition_E'] = transition_E.tolist()
    
    # 保存修复后的文件
    print(f"\n保存修复后的元信息到: {meta_file_path}")
    with open(meta_file_path, 'w') as f:
        json.dump(meta_dict, f, indent=2)
    
    # 验证修复结果
    print("\n修复后的转移矩阵统计:")
    no_bond_dominant_after = 0
    has_bonds_after = 0
    
    for i in range(len(transition_E)):
        for j in range(len(transition_E[i])):
            no_bond_prob = transition_E[i][j][0]
            bond_prob = sum(transition_E[i][j][1:])
            
            if no_bond_prob > 0.9:
                no_bond_dominant_after += 1
            if bond_prob > 0.01:
                has_bonds_after += 1
    
    print(f"无键概率>90%的对数: {no_bond_dominant_after} ({no_bond_dominant_after/total_pairs*100:.1f}%)")
    print(f"有键概率>1%的对数: {has_bonds_after} ({has_bonds_after/total_pairs*100:.1f}%)")
    
    improvement = has_bonds_after - has_bonds
    print(f"改善: +{improvement} 个有键原子对 ({improvement/total_pairs*100:.1f}%)")
    
    return True

def main():
    """主函数"""
    meta_file = "data/raw/fgfr1_with_toxic_solubility.meta.json"
    
    if not os.path.exists(meta_file):
        print(f"错误: 找不到元信息文件 {meta_file}")
        return
    
    print("=" * 60)
    print("Graph-DiT 转移矩阵修复工具")
    print("=" * 60)
    
    try:
        success = fix_transition_matrix(meta_file)
        
        if success:
            print("\n🎉 转移矩阵修复完成!")
            print("\n建议步骤:")
            print("1. 删除已处理的数据缓存: rm -rf data/processed/")
            print("2. 重新开始训练")
            print("3. 检查生成的分子是否有效")
        else:
            print("\n❌ 修复失败")
            
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
