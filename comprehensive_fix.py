#!/usr/bin/env python3
"""
全面修复数据集问题的脚本
解决原子类型分布和转移矩阵的问题
"""

import json
import os
import numpy as np

def fix_comprehensive_issues(meta_file_path):
    """
    全面修复数据集的问题
    """
    print(f"读取元信息文件: {meta_file_path}")
    
    # 备份原文件
    backup_path = meta_file_path + ".backup2"
    if not os.path.exists(backup_path):
        print(f"备份原文件到: {backup_path}")
        with open(meta_file_path, 'r') as f:
            content = f.read()
        with open(backup_path, 'w') as f:
            f.write(content)
    
    # 读取元信息
    with open(meta_file_path, 'r') as f:
        meta_dict = json.load(f)
    
    print("修复前的问题:")
    print(f"活跃原子: {meta_dict['active_atoms']}")
    
    # 修复原子类型分布
    print("\n修复原子类型分布...")
    
    # 正确的原子类型分布（基于常见有机分子）
    correct_atom_dist = [0.0] * 118  # 初始化为0
    
    # 设置正确的原子分布（按照active_atoms的顺序）
    # active_atoms: ['C', 'N', 'O', 'F', 'Na', 'P', 'S', 'Cl', 'Br', 'I']
    # 索引:         [0,   1,   2,   3,   4,    5,   6,   7,    8,    9]
    correct_atom_dist[0] = 0.72   # C - 碳原子占主导
    correct_atom_dist[1] = 0.165  # N - 氮原子
    correct_atom_dist[2] = 0.08   # O - 氧原子
    correct_atom_dist[3] = 0.017  # F - 氟原子
    correct_atom_dist[4] = 0.0    # Na - 钠原子（不应该存在）
    correct_atom_dist[5] = 0.005  # P - 磷原子
    correct_atom_dist[6] = 0.012  # S - 硫原子
    correct_atom_dist[7] = 0.001  # Cl - 氯原子
    correct_atom_dist[8] = 0.0001 # Br - 溴原子
    correct_atom_dist[9] = 0.00005 # I - 碘原子
    
    meta_dict['atom_type_dist'] = correct_atom_dist
    
    print("修复后的原子分布:")
    for i, atom in enumerate(meta_dict['active_atoms']):
        if i < len(correct_atom_dist):
            print(f"  {atom}: {correct_atom_dist[i]:.4f}")
    
    # 修复转移矩阵
    print("\n修复转移矩阵...")
    transition_E = np.array(meta_dict['transition_E'])
    
    # 原子索引映射
    atom_numbers = {'C': 6, 'N': 7, 'O': 8, 'F': 9, 'P': 15, 'S': 16, 'Cl': 17, 'Br': 35, 'I': 53}
    atom_to_index = {}
    for atom in meta_dict['active_atoms']:
        if atom in atom_numbers:
            atom_to_index[atom] = atom_numbers[atom] - 2
    
    # 更全面的化学键模式
    # [无键, 单键, 双键, 三键, 芳香键]
    bond_patterns = {
        ('C', 'C'): [0.2, 0.6, 0.15, 0.02, 0.03],   # C-C键：更多单键
        ('C', 'N'): [0.3, 0.55, 0.12, 0.01, 0.02],  # C-N键
        ('C', 'O'): [0.4, 0.5, 0.08, 0.0, 0.02],    # C-O键
        ('C', 'F'): [0.6, 0.4, 0.0, 0.0, 0.0],      # C-F键：只有单键
        ('C', 'Cl'): [0.7, 0.3, 0.0, 0.0, 0.0],     # C-Cl键
        ('C', 'Br'): [0.8, 0.2, 0.0, 0.0, 0.0],     # C-Br键
        ('C', 'I'): [0.85, 0.15, 0.0, 0.0, 0.0],    # C-I键
        ('C', 'S'): [0.5, 0.4, 0.08, 0.0, 0.02],    # C-S键
        ('C', 'P'): [0.7, 0.25, 0.05, 0.0, 0.0],    # C-P键
        ('N', 'N'): [0.7, 0.2, 0.08, 0.02, 0.0],    # N-N键
        ('N', 'O'): [0.6, 0.3, 0.08, 0.0, 0.02],    # N-O键
        ('N', 'H'): [0.3, 0.7, 0.0, 0.0, 0.0],      # N-H键（如果有氢）
        ('O', 'O'): [0.8, 0.15, 0.05, 0.0, 0.0],    # O-O键
        ('O', 'H'): [0.2, 0.8, 0.0, 0.0, 0.0],      # O-H键（如果有氢）
        ('S', 'S'): [0.8, 0.15, 0.05, 0.0, 0.0],    # S-S键
        ('P', 'O'): [0.6, 0.35, 0.05, 0.0, 0.0],    # P-O键
        ('F', 'F'): [0.95, 0.05, 0.0, 0.0, 0.0],    # F-F键（很少）
        ('Cl', 'Cl'): [0.95, 0.05, 0.0, 0.0, 0.0],  # Cl-Cl键（很少）
    }
    
    # 应用化学键模式
    fixed_pairs = 0
    for (atom1, atom2), probs in bond_patterns.items():
        if atom1 in atom_to_index and atom2 in atom_to_index:
            i = atom_to_index[atom1]
            j = atom_to_index[atom2]
            
            # 设置双向转移概率
            transition_E[i][j] = probs
            transition_E[j][i] = probs
            fixed_pairs += 2
            
            print(f"修复 {atom1}-{atom2} 键: 无键={probs[0]:.2f}, 单键={probs[1]:.2f}, 双键={probs[2]:.2f}")
    
    # 对于没有特定模式的原子对，设置默认的合理概率
    print(f"\n设置默认转移概率...")
    for i in range(len(transition_E)):
        for j in range(len(transition_E[i])):
            # 如果这个位置还是全1.0（即没有被修复），设置默认值
            if transition_E[i][j][0] == 1.0 and sum(transition_E[i][j][1:]) == 0.0:
                # 默认：大部分无键，少量单键
                transition_E[i][j] = [0.9, 0.08, 0.015, 0.003, 0.002]
    
    print(f"修复了 {fixed_pairs} 个特定原子对的转移概率")
    
    # 更新元信息
    meta_dict['transition_E'] = transition_E.tolist()

    # 修复活跃原子列表 - 移除分布为0的原子
    print("\n修复活跃原子列表...")
    original_active_atoms = meta_dict['active_atoms']
    old_atom_dist = meta_dict['atom_type_dist']
    print(f"原始活跃原子: {original_active_atoms}")
    print(f"原始原子分布: {old_atom_dist}")

    # 移除分布为0的原子（Na和P）
    new_active_atoms = []
    new_atom_dist = []
    for i, atom in enumerate(original_active_atoms):
        if i < len(old_atom_dist) and old_atom_dist[i] > 0:
            new_active_atoms.append(atom)
            new_atom_dist.append(old_atom_dist[i])
        else:
            print(f"移除原子 {atom}，分布为 {old_atom_dist[i] if i < len(old_atom_dist) else 'N/A'}")

    meta_dict['active_atoms'] = new_active_atoms
    meta_dict['num_atom_type'] = len(new_active_atoms)
    meta_dict['atom_type_dist'] = new_atom_dist

    print(f"修复后活跃原子: {new_active_atoms}")
    print(f"原子类型数量: {len(new_active_atoms)}")
    print(f"修复后原子分布: {new_atom_dist}")
    
    # 修复键类型分布，使其更合理
    print("\n修复键类型分布...")
    # 基于真实分子数据的键类型分布
    meta_dict['bond_type_dist'] = [
        0.85,    # 无键（减少）
        0.12,    # 单键（增加）
        0.02,    # 双键
        0.005,   # 三键
        0.025    # 芳香键
    ]
    
    # 保存修复后的文件
    print(f"\n保存修复后的元信息到: {meta_file_path}")
    with open(meta_file_path, 'w') as f:
        json.dump(meta_dict, f, indent=2)
    
    # 验证修复结果
    print("\n修复后的统计:")
    print("原子类型分布:")
    for i, atom in enumerate(meta_dict['active_atoms']):
        if i < len(meta_dict['atom_type_dist']):
            print(f"  {atom}: {meta_dict['atom_type_dist'][i]:.4f}")

    print(f"\n活跃原子列表: {meta_dict['active_atoms']}")
    print(f"原子类型数量: {meta_dict['num_atom_type']}")

    # 修复原子解码器映射
    print("\n修复原子解码器映射...")
    active_atoms = meta_dict['active_atoms']

    # 创建正确的原子解码器字典
    # 原子序数到符号的映射
    atom_numbers = {'C': 6, 'N': 7, 'O': 8, 'F': 9, 'P': 15, 'S': 16, 'Cl': 17, 'Br': 35, 'I': 53}

    # 创建从特征索引到原子符号的映射
    # 特征索引就是在活跃原子列表中的位置
    atom_decoder_dict = {}
    for i, atom in enumerate(active_atoms):
        atom_decoder_dict[i] = atom
        print(f"  {atom} -> 特征索引{i}")

    print(f"原子解码器字典: {atom_decoder_dict}")

    # 保存原子解码器字典到元信息中
    meta_dict['atom_decoder_dict'] = atom_decoder_dict
    
    print("\n键类型分布:")
    bond_names = ['无键', '单键', '双键', '三键', '芳香键']
    for i, (name, prob) in enumerate(zip(bond_names, meta_dict['bond_type_dist'])):
        print(f"  {name}: {prob:.4f}")
    
    # 统计转移矩阵改善
    reasonable_pairs = 0
    total_pairs = len(transition_E) * len(transition_E[0])
    
    for i in range(len(transition_E)):
        for j in range(len(transition_E[i])):
            bond_prob = sum(transition_E[i][j][1:])
            if bond_prob > 0.05:  # 有合理的键形成概率
                reasonable_pairs += 1
    
    print(f"\n转移矩阵统计:")
    print(f"有合理键概率(>5%)的原子对: {reasonable_pairs}/{total_pairs} ({reasonable_pairs/total_pairs*100:.2f}%)")
    
    return True

def main():
    """主函数"""
    meta_file = "data/raw/fgfr1_with_toxic_solubility.meta.json"
    
    if not os.path.exists(meta_file):
        print(f"错误: 找不到元信息文件 {meta_file}")
        return
    
    print("=" * 60)
    print("Graph-DiT 全面修复工具")
    print("=" * 60)
    
    try:
        success = fix_comprehensive_issues(meta_file)
        
        if success:
            print("\n🎉 全面修复完成!")
            print("\n建议步骤:")
            print("1. 删除已处理的数据缓存: rm -rf data/processed/")
            print("2. 重新开始训练")
            print("3. 监控生成分子的有效性")
            print("4. 如果仍有问题，可能需要重新生成整个数据集")
        else:
            print("\n❌ 修复失败")
            
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
