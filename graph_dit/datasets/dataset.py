import sys
sys.path.append('../') 

import os
import os.path as osp
import pathlib
import json

import torch
import torch.nn.functional as F
from rdkit import Chem, RDLogger
from rdkit.Chem.rdchem import BondType as BT
from tqdm import tqdm
import numpy as np
import pandas as pd
from torch_geometric.data import Data, InMemoryDataset
from torch_geometric.loader import DataLoader
from sklearn.model_selection import train_test_split

import utils as utils
from datasets.abstract_dataset import AbstractDatasetInfos, AbstractDataModule
from diffusion.distributions import DistributionNodes
import random

bonds = {BT.SINGLE: 1, BT.DOUBLE: 2, BT.TRIPLE: 3, BT.AROMATIC: 4}

# 化学验证相关常量
EXTENDED_ATOM_VALENCY = {
    1: [1],                    # H
    6: [2, 3, 4],             # C (考虑不饱和度)
    7: [1, 2, 3, 4, 5],       # N (考虑不同氧化态)
    8: [1, 2],                # O (考虑自由基)
    9: [1],                   # F
    15: [3, 5],               # P
    16: [2, 4, 6],            # S
    17: [1, 3, 5, 7],         # Cl (考虑高氧化态)
    35: [1, 3, 5],            # Br
    53: [1, 3, 5, 7]          # I
}

BOND_ORDER = {1: 1, 2: 2, 3: 3, 4: 1.5}  # 键类型对应的键级

class MoleculeValidator:
    """分子化学有效性验证器"""

    def __init__(self):
        self.validation_stats = {
            'total_processed': 0,
            'rdkit_sanitization_failed': 0,
            'valency_violations': 0,
            'bond_type_violations': 0,
            'valid_molecules': 0
        }

    def validate_molecule(self, mol, smiles=None):
        """
        验证分子的化学有效性

        Args:
            mol: RDKit分子对象
            smiles: SMILES字符串（可选）

        Returns:
            is_valid: 是否有效
            error_msg: 错误信息
        """
        self.validation_stats['total_processed'] += 1

        if mol is None:
            return False, "Invalid molecule object"

        # 1. RDKit分子净化检查
        try:
            Chem.SanitizeMol(mol)
        except Exception as e:
            self.validation_stats['rdkit_sanitization_failed'] += 1
            return False, f"RDKit sanitization failed: {str(e)}"

        # 2. 原子价态检查
        valency_valid, valency_error = self._check_atom_valencies(mol)
        if not valency_valid:
            self.validation_stats['valency_violations'] += 1
            return False, f"Valency violation: {valency_error}"

        # 3. 键类型合理性检查
        bond_valid, bond_error = self._check_bond_types(mol)
        if not bond_valid:
            self.validation_stats['bond_type_violations'] += 1
            return False, f"Bond type violation: {bond_error}"

        self.validation_stats['valid_molecules'] += 1
        return True, "Valid molecule"

    def _check_atom_valencies(self, mol):
        """检查原子价态是否合理"""
        for atom in mol.GetAtoms():
            atom_num = atom.GetAtomicNum()

            # 计算实际价态
            actual_valency = 0
            for bond in atom.GetBonds():
                bond_type = bond.GetBondType()
                if bond_type == BT.SINGLE:
                    actual_valency += 1
                elif bond_type == BT.DOUBLE:
                    actual_valency += 2
                elif bond_type == BT.TRIPLE:
                    actual_valency += 3
                elif bond_type == BT.AROMATIC:
                    actual_valency += 1.5

            # 检查是否在允许范围内
            allowed_valencies = EXTENDED_ATOM_VALENCY.get(atom_num, [4])
            if actual_valency not in allowed_valencies:
                return False, f"Atom {atom_num} has valency {actual_valency}, allowed: {allowed_valencies}"

        return True, ""

    def _check_bond_types(self, mol):
        """检查键类型是否合理"""
        for bond in mol.GetBonds():
            atom1 = bond.GetBeginAtom()
            atom2 = bond.GetEndAtom()
            bond_type = bond.GetBondType()

            atom1_num = atom1.GetAtomicNum()
            atom2_num = atom2.GetAtomicNum()

            # 氢原子不能形成多重键
            if (atom1_num == 1 or atom2_num == 1) and bond_type != BT.SINGLE:
                return False, f"Hydrogen cannot form multiple bonds: {atom1_num}-{atom2_num} bond type {bond_type}"

            # 卤素原子通常只形成单键
            halogens = {9, 17, 35, 53}  # F, Cl, Br, I
            if (atom1_num in halogens or atom2_num in halogens) and bond_type != BT.SINGLE:
                return False, f"Halogen atoms should only form single bonds: {atom1_num}-{atom2_num} bond type {bond_type}"

            # 三重键只能在特定原子间形成
            if bond_type == BT.TRIPLE:
                valid_triple_atoms = {6, 7}  # C, N
                if atom1_num not in valid_triple_atoms or atom2_num not in valid_triple_atoms:
                    return False, f"Triple bond not allowed between atoms {atom1_num} and {atom2_num}"

        return True, ""

    def get_validation_stats(self):
        """获取验证统计信息"""
        return self.validation_stats.copy()

    def print_validation_summary(self):
        """打印验证摘要"""
        stats = self.validation_stats
        total = stats['total_processed']
        if total == 0:
            print("No molecules processed for validation.")
            return

        print(f"\n=== 分子验证摘要 ===")
        print(f"总处理分子数: {total}")
        print(f"有效分子数: {stats['valid_molecules']} ({stats['valid_molecules']/total*100:.2f}%)")
        print(f"RDKit净化失败: {stats['rdkit_sanitization_failed']} ({stats['rdkit_sanitization_failed']/total*100:.2f}%)")
        print(f"价态违规: {stats['valency_violations']} ({stats['valency_violations']/total*100:.2f}%)")
        print(f"键类型违规: {stats['bond_type_violations']} ({stats['bond_type_violations']/total*100:.2f}%)")
        print("=" * 25)

class DataModule(AbstractDataModule):
    def __init__(self, cfg):
        self.datadir = cfg.data.datadir
        self.task = cfg.data.task_name
        super().__init__(cfg)

    def prepare_data(self) -> None:
        target = getattr(self.cfg.data, 'guidance_target', None)
        base_path = pathlib.Path(os.path.realpath(__file__)).parents[2]
        root_path = os.path.join(base_path, self.datadir)
        self.root_path = root_path

        batch_size = self.cfg.train.batch_size
        num_workers = self.cfg.train.num_workers
        pin_memory = self.cfg.data.pin_memory

        dataset = Dataset(source=self.task, root=root_path, target_prop=target, transform=None)

        if self.task == 'FGFR1_IC50' or self.task == 'FGFR1_toxicity' or self.task == 'fgfr1_with_toxic_solubility':
            train_index, val_index, test_index, unlabeled_index = self.random_data_split(dataset)
        elif len(self.task.split('-')) == 2:
            train_index, val_index, test_index, unlabeled_index = self.fixed_split(dataset)
        else:
            train_index, val_index, test_index, unlabeled_index = self.random_data_split(dataset)

        self.train_index, self.val_index, self.test_index, self.unlabeled_index = train_index, val_index, test_index, unlabeled_index
        train_index, val_index, test_index, unlabeled_index = torch.LongTensor(train_index), torch.LongTensor(val_index), torch.LongTensor(test_index), torch.LongTensor(unlabeled_index)
        if len(unlabeled_index) > 0:
            train_index = torch.cat([train_index, unlabeled_index], dim=0)
        
        train_dataset, val_dataset, test_dataset = dataset[train_index], dataset[val_index], dataset[test_index]
        self.train_dataset = train_dataset
        
        # 使用自定义的collate函数
        self.train_loader = DataLoader(train_dataset, batch_size=batch_size, num_workers=num_workers, shuffle=True, pin_memory=pin_memory, collate_fn=self.custom_collate)
        self.val_loader = DataLoader(val_dataset, batch_size=batch_size, num_workers=num_workers, shuffle=False, pin_memory=False, collate_fn=self.custom_collate)
        self.test_loader = DataLoader(test_dataset, batch_size=batch_size, num_workers=num_workers, shuffle=False, pin_memory=False, collate_fn=self.custom_collate)

        training_iterations = len(train_dataset) // batch_size
        self.training_iterations = training_iterations
    
    def random_data_split(self, dataset):
        nan_count = torch.isnan(dataset.y[:, 0]).sum().item()
        labeled_len = len(dataset) - nan_count
        full_idx = list(range(labeled_len))
        train_ratio, valid_ratio, test_ratio = 0.6, 0.2, 0.2
        train_index, test_index, _, _ = train_test_split(full_idx, full_idx, test_size=test_ratio, random_state=42)
        train_index, val_index, _, _ = train_test_split(train_index, train_index, test_size=valid_ratio/(valid_ratio+train_ratio), random_state=42)
        unlabeled_index = list(range(labeled_len, len(dataset)))
        print(self.task, ' dataset len', len(dataset), 'train len', len(train_index), 'val len', len(val_index), 'test len', len(test_index), 'unlabeled len', len(unlabeled_index))
        return train_index, val_index, test_index, unlabeled_index
    
    def fixed_split(self, dataset):
        if self.task == 'O2-N2':
            test_index = [42,43,92,122,197,198,251,254,257,355,511,512,549,602,603,604]
        else:
            raise ValueError('Invalid task name: {}'.format(self.task))
        full_idx = list(range(len(dataset)))
        full_idx = list(set(full_idx) - set(test_index))
        train_ratio = 0.8
        train_index, val_index, _, _ = train_test_split(full_idx, full_idx, test_size=1-train_ratio, random_state=42)
        print(self.task, ' dataset len', len(dataset), 'train len', len(train_index), 'val len', len(val_index), 'test len', len(test_index))
        return train_index, val_index, test_index, []

    def get_train_smiles(self):
        # 检查是否有带props的版本
        props_filename = f'{self.task}_with_props.csv'
        filename = f'{self.task}.csv.gz'
        toxicity_filename = 'fgfr1_toxicity_predictions.csv'
        toxic_solubility_filename = 'fgfr1_with_toxic_solubility.csv'
        props_file_path = f'{self.root_path}/raw/{props_filename}'
        toxicity_file_path = f'{self.root_path}/raw/{toxicity_filename}'
        toxic_solubility_file_path = f'{self.root_path}/raw/{toxic_solubility_filename}'

        if self.task == 'FGFR1_IC50' and os.path.exists(props_file_path):
            df = pd.read_csv(props_file_path)
        elif self.task == 'FGFR1_toxicity' and os.path.exists(toxicity_file_path):
            df = pd.read_csv(toxicity_file_path)
        elif self.task == 'fgfr1_with_toxic_solubility' and os.path.exists(toxic_solubility_file_path):
            df = pd.read_csv(toxic_solubility_file_path)
        else:
            df = pd.read_csv(f'{self.root_path}/raw/{filename}')

        df_test = df.iloc[self.test_index]
        df = df.iloc[self.train_index]
        smiles_list = df['smiles'].tolist()
        smiles_list_test = df_test['smiles'].tolist()
        smiles_list = [Chem.MolToSmiles(Chem.MolFromSmiles(smi)) for smi in smiles_list]
        smiles_list_test = [Chem.MolToSmiles(Chem.MolFromSmiles(smi)) for smi in smiles_list_test]
        return smiles_list, smiles_list_test
    
    def get_data_split(self):
        # 检查是否有带props的版本
        props_filename = f'{self.task}_with_props.csv'
        filename = f'{self.task}.csv.gz'
        toxicity_filename = 'fgfr1_toxicity_predictions.csv'
        toxic_solubility_filename = 'fgfr1_with_toxic_solubility.csv'
        props_file_path = f'{self.root_path}/raw/{props_filename}'
        toxicity_file_path = f'{self.root_path}/raw/{toxicity_filename}'
        toxic_solubility_file_path = f'{self.root_path}/raw/{toxic_solubility_filename}'

        if self.task == 'FGFR1_IC50' and os.path.exists(props_file_path):
            df = pd.read_csv(props_file_path)
        elif self.task == 'FGFR1_toxicity' and os.path.exists(toxicity_file_path):
            df = pd.read_csv(toxicity_file_path)
        elif self.task == 'fgfr1_with_toxic_solubility' and os.path.exists(toxic_solubility_file_path):
            df = pd.read_csv(toxic_solubility_file_path)
        else:
            df = pd.read_csv(f'{self.root_path}/raw/{filename}')

        df_val = df.iloc[self.val_index]
        df_test = df.iloc[self.test_index]
        df_train = df.iloc[self.train_index]
        return df_train, df_val, df_test

    def example_batch(self):
        return next(iter(self.val_loader))
    
    def train_dataloader(self):
        return self.train_loader

    def val_dataloader(self):
        return self.val_loader
    
    def test_dataloader(self):
        return self.test_loader

    def custom_collate(self, data_list):
        """自定义collate函数，处理SMILES属性"""
        # 收集所有SMILES
        smiles_list = []
        for data in data_list:
            if hasattr(data, 'smiles') and data.smiles:
                if isinstance(data.smiles, list):
                    smiles_list.extend(data.smiles)
                else:
                    smiles_list.append(data.smiles)
            else:
                smiles_list.append("")  # 如果没有SMILES，添加空字符串

        # 使用PyG的默认collate函数
        from torch_geometric.data import Batch
        batch = Batch.from_data_list(data_list)

        # 添加SMILES属性到批处理数据
        if smiles_list:
            batch.smiles = smiles_list

        return batch


class Dataset(InMemoryDataset):
    def __init__(self, source, root, target_prop=None,
                 transform=None, pre_transform=None, pre_filter=None):
        self.source = source
        self.target_prop = target_prop
        self.smiles_list = []
        super().__init__(root, transform, pre_transform, pre_filter)
        self.data, self.slices = torch.load(self.processed_paths[0])
        self.load_and_add_smiles()
        
    def load_and_add_smiles(self):
        """从原始CSV文件加载SMILES并添加到数据对象中"""
        try:
            data_path = osp.join(self.raw_dir, self.raw_file_names[0])
            
            # 根据文件类型选择合适的读取方式
            if data_path.endswith('.gz'):
                data_df = pd.read_csv(data_path, compression='gzip')
            else:
                data_df = pd.read_csv(data_path)
            
            # 确保数据框中有smiles列
            if 'smiles' in data_df.columns:
                smiles_list = data_df['smiles'].tolist()
                
                # 为每个数据点添加SMILES属性
                if not hasattr(self.data, 'smiles'):
                    self.data.smiles = smiles_list
                    # 简化日志输出，只显示加载成功
                    print("成功加载SMILES数据")
            else:
                # 简化错误信息
                print("未找到SMILES列")
        except Exception as e:
            # 简化错误信息
            error_msg = str(e)
            if "\n" in error_msg:
                error_msg = error_msg.split("\n")[0]
            print("SMILES加载失败:", error_msg)
    
    @property
    def raw_file_names(self):
        # 优先使用带props的版本
        props_filename = f'{self.source}_with_props.csv'
        props_path = osp.join(self.raw_dir, props_filename)
        toxicity_filename = 'fgfr1_toxicity_predictions.csv'
        toxicity_path = osp.join(self.raw_dir, toxicity_filename)

        if self.source == 'FGFR1_IC50' and osp.exists(props_path):
            return [props_filename]
        elif self.source == 'FGFR1_toxicity' and osp.exists(toxicity_path):
            return [toxicity_filename]
        elif self.source == 'fgfr1_with_toxic_solubility':
            return ['fgfr1_with_toxic_solubility.csv']
        else:
            return [f'{self.source}.csv.gz']
    
    @property
    def processed_file_names(self):
        return [f'{self.source}.pt']

    def process(self):
        RDLogger.DisableLog('rdApp.*')

        # 读取数据集
        raw_file_path = os.path.join(self.raw_dir, self.raw_file_names[0])
        if self.raw_file_names[0] == 'fgfr1_with_toxic_solubility.csv':
            df = pd.read_csv(raw_file_path)
            smiles_list = df['smiles'].tolist()
            target_list = df['label'].tolist() if 'label' in df.columns else [float('nan')] * len(smiles_list)
            sa_list = df['SA'].tolist() if 'SA' in df.columns else [float('nan')] * len(smiles_list)
            sc_list = df['SC'].tolist() if 'SC' in df.columns else [float('nan')] * len(smiles_list)
            ld50_list = df['ld50'].tolist() if 'ld50' in df.columns else [float('nan')] * len(smiles_list)
            solubility_list = df['solubility'].tolist() if 'solubility' in df.columns else [float('nan')] * len(smiles_list)
        elif self.raw_file_names[0] == 'fgfr1_toxicity_predictions.csv':
            df = pd.read_csv(raw_file_path)
            smiles_list = df['smiles'].tolist()
            target_list = df['label'].tolist() if 'label' in df.columns else [float('nan')] * len(smiles_list)
            sa_list = df['SA'].tolist() if 'SA' in df.columns else [float('nan')] * len(smiles_list)
            sc_list = df['SC'].tolist() if 'SC' in df.columns else [float('nan')] * len(smiles_list)
            ld50_list = df['ld50'].tolist() if 'ld50' in df.columns else [float('nan')] * len(smiles_list)
            solubility_list = [float('nan')] * len(smiles_list)
        elif self.raw_file_names[0].endswith('_with_props.csv'):
            df = pd.read_csv(raw_file_path)
            smiles_list = df['smiles'].tolist()
            target_list = df['label'].tolist() if 'label' in df.columns else [float('nan')] * len(smiles_list)
            sa_list = df['SA'].tolist() if 'SA' in df.columns else [float('nan')] * len(smiles_list)
            sc_list = df['SC'].tolist() if 'SC' in df.columns else [float('nan')] * len(smiles_list)
            ld50_list = [float('nan')] * len(smiles_list)
            solubility_list = [float('nan')] * len(smiles_list)
        else:
            df = pd.read_csv(raw_file_path)
            smiles_list = df['smiles'].tolist()
            target_list = df['label'].tolist() if 'label' in df.columns else [float('nan')] * len(smiles_list)
            sa_list = [float('nan')] * len(smiles_list)
            sc_list = [float('nan')] * len(smiles_list)
            ld50_list = [float('nan')] * len(smiles_list)
            solubility_list = [float('nan')] * len(smiles_list)
        
        # 保存SMILES列表
        self.smiles_list = smiles_list
        
        # 初始化分子验证器
        molecule_validator = MoleculeValidator()

        # 处理分子图
        data_list = []
        invalid_molecules = []

        for i, smi in enumerate(tqdm(smiles_list, desc='Processing molecules')):
            mol = Chem.MolFromSmiles(smi)
            if mol is None:
                invalid_molecules.append({'index': i, 'smiles': smi, 'error': 'Failed to parse SMILES'})
                continue

            target = target_list[i] if i < len(target_list) else float('nan')
            sa = sa_list[i] if i < len(sa_list) else float('nan')
            sc = sc_list[i] if i < len(sc_list) else float('nan')

            # 添加ld50和solubility作为额外目标值
            ld50 = ld50_list[i] if 'ld50_list' in locals() and i < len(ld50_list) else float('nan')
            solubility = solubility_list[i] if 'solubility_list' in locals() and i < len(solubility_list) else float('nan')

            try:
                # 修改为传递ld50作为target3，solubility作为target4
                data = self.mol_to_graph(mol, sa, sc, target, target2=None, target3=ld50, target4=solubility, smiles=smi)
                data_list.append(data)
            except ValueError as e:
                # 记录化学验证失败的分子
                invalid_molecules.append({'index': i, 'smiles': smi, 'error': str(e)})
                continue
            except Exception as e:
                # 记录其他处理失败的分子
                invalid_molecules.append({'index': i, 'smiles': smi, 'error': f'Processing failed: {str(e)}'})
                continue

        # 打印验证统计信息
        if hasattr(self, 'molecule_validator'):
            self.molecule_validator.print_validation_summary()

        # 保存无效分子信息用于调试
        if invalid_molecules:
            print(f"\n发现 {len(invalid_molecules)} 个无效分子，占总数的 {len(invalid_molecules)/len(smiles_list)*100:.2f}%")
            # 保存前10个无效分子的详细信息
            print("前10个无效分子示例:")
            for invalid_mol in invalid_molecules[:10]:
                print(f"  索引 {invalid_mol['index']}: {invalid_mol['smiles']} - {invalid_mol['error']}")

            # 可选：保存完整的无效分子列表到文件
            invalid_file_path = osp.join(self.processed_dir, 'invalid_molecules.json')
            with open(invalid_file_path, 'w') as f:
                json.dump(invalid_molecules, f, indent=2)
            print(f"完整的无效分子列表已保存到: {invalid_file_path}")
                
        if self.pre_filter is not None:
            data_list = [data for data in data_list if self.pre_filter(data)]

        if self.pre_transform is not None:
            data_list = [self.pre_transform(data) for data in data_list]

        data, slices = self.collate(data_list)
        torch.save((data, slices), self.processed_paths[0])

        # 保存处理统计信息
        processing_stats = {
            'total_input_molecules': len(smiles_list),
            'valid_molecules': len(data_list),
            'invalid_molecules': len(invalid_molecules),
            'validation_stats': molecule_validator.get_validation_stats() if 'molecule_validator' in locals() else {}
        }

        stats_file_path = osp.join(self.processed_dir, 'processing_stats.json')
        with open(stats_file_path, 'w') as f:
            json.dump(processing_stats, f, indent=2)

        print(f"\n=== 数据处理完成 ===")
        print(f"输入分子总数: {processing_stats['total_input_molecules']}")
        print(f"有效分子数: {processing_stats['valid_molecules']}")
        print(f"无效分子数: {processing_stats['invalid_molecules']}")
        print(f"有效率: {processing_stats['valid_molecules']/processing_stats['total_input_molecules']*100:.2f}%")
        print(f"处理统计已保存到: {stats_file_path}")
        print("=" * 25)

    def mol_to_graph(self, mol, sa, sc, target, target2=None, target3=None, target4=None, valid_atoms=None, smiles=None):
        # 化学有效性验证
        if not hasattr(self, 'molecule_validator'):
            self.molecule_validator = MoleculeValidator()

        is_valid, error_msg = self.molecule_validator.validate_molecule(mol, smiles)
        if not is_valid:
            raise ValueError(f"Invalid molecule: {error_msg}")

        # 节点特征
        atom_features_list = []
        for atom in mol.GetAtoms():
            atom_features_list.append(self.atom_to_feature_vector(atom))
        x = torch.tensor(np.array(atom_features_list), dtype=torch.long)

        # 边特征
        edges_list = []
        edge_features_list = []
        for bond in mol.GetBonds():
            i = bond.GetBeginAtomIdx()
            j = bond.GetEndAtomIdx()
            edge_feature = bonds[bond.GetBondType()]
            # 添加两个方向的边
            edges_list.append((i, j))
            edge_features_list.append(edge_feature)
            edges_list.append((j, i))
            edge_features_list.append(edge_feature)

        edge_index = torch.tensor(np.array(edges_list).T, dtype=torch.long)
        edge_attr = torch.tensor(np.array(edge_features_list), dtype=torch.long)

        # 构建包含所有目标值的y向量
        # 对于fgfr1_with_toxic_solubility数据集，顺序为：[ld50, SA, SC, label, solubility]
        y_values = []

        # 根据数据集类型确定y向量的构成
        if target3 is not None or target4 is not None:  # 新的数据集格式
            # 顺序：[ld50, SA, SC, label, solubility]
            y_values = [
                target3 if target3 is not None and not np.isnan(target3) else float('nan'),  # ld50
                sa if sa is not None and not np.isnan(sa) else float('nan'),                # SA
                sc if sc is not None and not np.isnan(sc) else float('nan'),                # SC
                target if target is not None and not np.isnan(target) else float('nan'),    # label
                target4 if target4 is not None and not np.isnan(target4) else float('nan')  # solubility
            ]
        else:  # 兼容旧的数据集格式
            # 顺序：[SA, SC, label]
            y_values = [
                sa if sa is not None and not np.isnan(sa) else float('nan'),                # SA
                sc if sc is not None and not np.isnan(sc) else float('nan'),                # SC
                target if target is not None and not np.isnan(target) else float('nan')     # label
            ]

        y = torch.tensor([y_values], dtype=torch.float)

        data = Data(
            x=x,
            edge_index=edge_index,
            edge_attr=edge_attr,
            y=y,
            sa=torch.tensor([sa], dtype=torch.float),
            sc=torch.tensor([sc], dtype=torch.float)
        )

        # 添加SMILES
        if smiles is not None:
            data.smiles = smiles

        return data

    def atom_to_feature_vector(self, atom):
        """
        将原子转换为特征向量
        """
        # 原子类型 (1-118)
        atom_type = atom.GetAtomicNum() - 1
        
        # 确保原子类型在有效范围内
        if atom_type < 0:
            atom_type = 118  # 使用119作为未知原子类型的索引
            
        return atom_type


class DataInfos(AbstractDatasetInfos):
    def __init__(self, datamodule, cfg):
        tasktype_dict = {
            'hiv_b': 'classification',
            'bace_b': 'classification',
            'bbbp_b': 'classification',
            'O2': 'regression',
            'N2': 'regression',
            'CO2': 'regression',
            'FGFR1_IC50': 'regression',  # 添加FGFR1_IC50为回归任务
        }
        task_name = cfg.data.task_name
        self.task = task_name
        self.task_type = tasktype_dict.get(task_name, "regression")
        self.ensure_connected = cfg.model.ensure_connected

        datadir = cfg.data.datadir

        base_path = pathlib.Path(os.path.realpath(__file__)).parents[2]
        meta_filename = os.path.join(base_path, datadir, 'raw', f'{task_name}.meta.json')
        data_root = os.path.join(base_path, datadir, 'raw')
        if os.path.exists(meta_filename):
            with open(meta_filename, 'r') as f:
                meta_dict = json.load(f)
        else:
            meta_dict = compute_meta(data_root, task_name, datamodule.train_index, datamodule.test_index)

        self.base_path = base_path
        self.active_atoms = meta_dict['active_atoms']
        self.max_n_nodes = meta_dict['max_node']
        self.original_max_n_nodes = meta_dict['max_node']
        
        # 限制最大节点数为50，以防止内存溢出
        if self.max_n_nodes > 50:
            print(f"原始最大节点数为 {self.max_n_nodes}，限制为 50")
            self.max_n_nodes = 50
        
        self.n_nodes = torch.Tensor(meta_dict['n_atoms_per_mol_dist'])
        self.edge_types = torch.Tensor(meta_dict['bond_type_dist'])
        self.transition_E = torch.Tensor(meta_dict['transition_E'])

        # 创建正确的原子解码器字典
        # 原子解码器应该是从特征索引（0,1,2,...）到原子符号的映射
        active_atoms = meta_dict['active_atoms']
        self.atom_decoder = {}
        for i, atom in enumerate(active_atoms):
            self.atom_decoder[i] = atom
        print(f"创建原子解码器字典: {self.atom_decoder}")
        print(f"活跃原子列表: {active_atoms}")
        print(f"原子类型数量: {len(active_atoms)}")
        node_types = torch.Tensor(meta_dict['atom_type_dist'])
        active_index = (node_types > 0).nonzero().squeeze()
        self.node_types = torch.Tensor(meta_dict['atom_type_dist'])[active_index]
        self.nodes_dist = DistributionNodes(self.n_nodes)
        self.active_index = active_index

        val_len = 3 * self.original_max_n_nodes - 2
        meta_val = torch.Tensor(meta_dict['valencies'])
        self.valency_distribution = torch.zeros(val_len)
        val_len = min(val_len, len(meta_val))
        self.valency_distribution[:val_len] = meta_val[:val_len]
        self.y_prior = None
        
        # 计算并保存训练集中y的统计数据
        train_df, _, _ = datamodule.get_data_split()
        self.train_y_stats = {}

        # 根据任务名称确定属性列
        if task_name == 'fgfr1_with_toxic_solubility':
            prop_cols = ['ld50', 'SA', 'SC', 'label', 'solubility']
        else:
            # 兼容旧的任务
            prop_cols = ['SA', 'SC', 'label', 'ld50']

        for i, col in enumerate(prop_cols):
            if col in train_df.columns:
                self.train_y_stats[i] = {
                    'min': train_df[col].min(),
                    'max': train_df[col].max(),
                    'mean': train_df[col].mean(),
                    'std': train_df[col].std(),
                    'name': col
                }


def compute_meta(root, source_name, train_index, test_index):
    pt = Chem.GetPeriodicTable()
    atom_name_list = []
    atom_count_list = []
    for i in range(2, 119):
        atom_name_list.append(pt.GetElementSymbol(i))
        atom_count_list.append(0)
    atom_name_list.append('*')
    atom_count_list.append(0)
    n_atoms_per_mol = [0] * 500
    bond_count_list = [0, 0, 0, 0, 0]
    bond_type_to_index =  {BT.SINGLE: 1, BT.DOUBLE: 2, BT.TRIPLE: 3, BT.AROMATIC: 4}
    valencies = [0] * 500
    tansition_E = np.zeros((118, 118, 5))
    
    # 检查是否有带props的版本
    props_filename = f'{source_name}_with_props.csv'
    filename = f'{source_name}.csv.gz'
    toxic_solubility_filename = 'fgfr1_with_toxic_solubility.csv'
    props_file_path = os.path.join(root, props_filename)
    toxic_solubility_file_path = os.path.join(root, toxic_solubility_filename)

    # 优先使用带props的版本
    if source_name == 'FGFR1_IC50' and os.path.exists(props_file_path):
        df = pd.read_csv(props_file_path)
    elif source_name == 'fgfr1_with_toxic_solubility' and os.path.exists(toxic_solubility_file_path):
        df = pd.read_csv(toxic_solubility_file_path)
    else:
        df = pd.read_csv(os.path.join(root, filename))
    
    # 添加SA和SC列，如果不存在
    if 'SA' not in df.columns:
        df['SA'] = 0.0
    if 'SC' not in df.columns:
        df['SC'] = 0.0
        
    all_index = list(range(len(df)))
    non_test_index = list(set(all_index) - set(test_index))
    df = df.iloc[non_test_index]
    tot_smiles = df['smiles'].tolist()

    n_atom_list = []
    n_bond_list = []
    skipped_count = 0
    for i, sms in enumerate(tot_smiles):
        try:
            mol = Chem.MolFromSmiles(sms)
            if mol is None:  # 跳过无效分子
                continue
            
            # 跳过原子数大于50的分子
            if mol.GetNumHeavyAtoms() > 50:
                skipped_count += 1
                continue
        except:
            continue

        n_atom = mol.GetNumHeavyAtoms()
        n_bond = mol.GetNumBonds()
        n_atom_list.append(n_atom)
        n_bond_list.append(n_bond)

        n_atoms_per_mol[n_atom] += 1
        cur_atom_count_arr = np.zeros(118)
        for atom in mol.GetAtoms():
            symbol = atom.GetSymbol()
            if symbol == 'H':
                continue
            elif symbol == '*':
                atom_count_list[-1] += 1
                cur_atom_count_arr[-1] += 1
            else:
                atom_count_list[atom.GetAtomicNum()-2] += 1
                cur_atom_count_arr[atom.GetAtomicNum()-2] += 1
                try:
                    valencies[int(atom.GetExplicitValence())] += 1
                except:
                    print('src', source_name,'int(atom.GetExplicitValence())', int(atom.GetExplicitValence()))
        
        tansition_E_temp = np.zeros((118, 118, 5))
        for bond in mol.GetBonds():
            start_atom, end_atom = bond.GetBeginAtom(), bond.GetEndAtom()
            if start_atom.GetSymbol() == 'H' or end_atom.GetSymbol() == 'H':
                continue
            
            if start_atom.GetSymbol() == '*':
                start_index = 117
            else:
                start_index = start_atom.GetAtomicNum() - 2
            if end_atom.GetSymbol() == '*':
                end_index = 117
            else:
                end_index = end_atom.GetAtomicNum() - 2

            bond_type = bond.GetBondType()
            bond_index = bond_type_to_index[bond_type]
            bond_count_list[bond_index] += 2

            tansition_E[start_index, end_index, bond_index] += 2
            tansition_E[end_index, start_index, bond_index] += 2
            tansition_E_temp[start_index, end_index, bond_index] += 2
            tansition_E_temp[end_index, start_index, bond_index] += 2

        bond_count_list[0] += n_atom * (n_atom - 1) - n_bond * 2
        cur_tot_bond = cur_atom_count_arr.reshape(-1,1) * cur_atom_count_arr.reshape(1,-1) * 2 # 118 * 118
        cur_tot_bond = cur_tot_bond - np.diag(cur_atom_count_arr) * 2 # 118 * 118
        tansition_E[:, :, 0] += cur_tot_bond - tansition_E_temp.sum(axis=-1)
        assert (cur_tot_bond > tansition_E_temp.sum(axis=-1)).sum() >= 0, f'i:{i}, sms:{sms}'
    
    if skipped_count > 0:
        print(f"跳过了 {skipped_count} 个原子数大于50的分子")
    
    if len(n_atom_list) == 0:
        print(f"警告: {source_name} 没有有效分子")
        n_atom_list = [10]  # 默认值
        n_bond_list = [10]  # 默认值
    
    n_atoms_per_mol = np.array(n_atoms_per_mol)
    n_atoms_sum = np.sum(n_atoms_per_mol)
    if n_atoms_sum > 0:
        n_atoms_per_mol = n_atoms_per_mol / n_atoms_sum
    else:
        # 如果没有有效的n_atoms_per_mol，设置默认值
        n_atoms_per_mol = np.zeros_like(n_atoms_per_mol)
        n_atoms_per_mol[10] = 1.0  # 设置10个原子为默认值
    
    n_atoms_per_mol = n_atoms_per_mol.tolist()[:51]

    atom_count_list = np.array(atom_count_list)
    atom_sum = np.sum(atom_count_list)
    if atom_sum > 0:
        atom_count_list = atom_count_list / atom_sum
    else:
        # 如果没有原子，设置默认值
        atom_count_list = np.zeros_like(atom_count_list)
        atom_count_list[4] = 0.5  # C原子
        atom_count_list[6] = 0.3  # N原子
        atom_count_list[7] = 0.2  # O原子
        
    print('processed meta info: ------', filename, '------')
    print('len atom_count_list', len(atom_count_list))
    print('len atom_name_list', len(atom_name_list))
    active_atoms = np.array(atom_name_list)[atom_count_list > 0]
    if len(active_atoms) == 0:
        # 如果没有活性原子，手动设置一些默认值
        active_indices = [4, 6, 7]  # C, N, O
        active_atoms = np.array(atom_name_list)[active_indices]
    
    active_atoms = active_atoms.tolist()
    atom_count_list = atom_count_list.tolist()

    bond_count_list = np.array(bond_count_list)
    bond_sum = np.sum(bond_count_list)
    if bond_sum > 0:
        bond_count_list = bond_count_list / bond_sum
    else:
        # 如果没有键，设置默认值
        bond_count_list = np.zeros_like(bond_count_list)
        bond_count_list[1] = 0.6  # 单键
        bond_count_list[2] = 0.2  # 双键
        bond_count_list[4] = 0.2  # 芳香键
        
    bond_count_list = bond_count_list.tolist()
    
    valencies = np.array(valencies)
    valencies_sum = np.sum(valencies)
    if valencies_sum > 0:
        valencies = valencies / valencies_sum
    else:
        # 如果没有价，设置默认值
        valencies = np.zeros_like(valencies)
        valencies[4] = 1.0  # 价为4设为1.0
        
    valencies = valencies.tolist()

    no_edge = np.sum(tansition_E, axis=-1) == 0
    first_elt = tansition_E[:, :, 0]
    first_elt[no_edge] = 1
    tansition_E[:, :, 0] = first_elt

    tansition_E_sum = np.sum(tansition_E, axis=-1, keepdims=True)
    # 避免除以0
    tansition_E_sum[tansition_E_sum == 0] = 1
    tansition_E = tansition_E / tansition_E_sum

    # 修复转移矩阵问题：确保有键的原子对有合理的转移概率
    print("修复转移矩阵...")
    for i in range(118):
        for j in range(118):
            # 如果这对原子在数据集中有键连接
            if np.sum(tansition_E[i, j, 1:]) > 0.01:  # 有实际的键
                # 重新分配概率，减少"无键"的概率
                total_bonds = np.sum(tansition_E[i, j, 1:])
                if total_bonds > 0:
                    # 设置无键概率为0.1，其余按比例分配给有键状态
                    tansition_E[i, j, 0] = 0.1
                    remaining_prob = 0.9
                    # 重新归一化键概率
                    bond_probs = tansition_E[i, j, 1:] / total_bonds * remaining_prob
                    tansition_E[i, j, 1:] = bond_probs
    
    meta_dict = {
        'source': source_name, 
        'num_graph': len(n_atom_list), 
        'n_atoms_per_mol_dist': n_atoms_per_mol,
        'max_node': max(n_atom_list) if n_atom_list else 10, 
        'max_bond': max(n_bond_list) if n_bond_list else 10, 
        'atom_type_dist': atom_count_list,
        'bond_type_dist': bond_count_list,
        'valencies': valencies,
        'active_atoms': active_atoms,
        'num_atom_type': len(active_atoms),
        'transition_E': tansition_E.tolist(),
        }

    with open(f'{root}/{source_name}.meta.json', "w") as f:
        json.dump(meta_dict, f)
    
    return meta_dict


# 新增：节点/边特征扰动增强函数
def random_node_edge_perturb(data, node_mask_prob=0.1, edge_mask_prob=0.1, node_noise_std=0.1, edge_noise_std=0.1):
    # 节点特征mask或加噪声
    if hasattr(data, 'x') and data.x is not None:
        mask = torch.rand(data.x.shape[0]) < node_mask_prob
        data.x[mask] = 0  # mask节点
        noise = torch.randn_like(data.x) * node_noise_std
        data.x = data.x + noise
    # 边特征mask或加噪声
    if hasattr(data, 'edge_attr') and data.edge_attr is not None:
        mask = torch.rand(data.edge_attr.shape[0]) < edge_mask_prob
        data.edge_attr[mask] = 0  # mask边
        noise = torch.randn_like(data.edge_attr) * edge_noise_std
        data.edge_attr = data.edge_attr + noise
    return data


if __name__ == "__main__":
    pass