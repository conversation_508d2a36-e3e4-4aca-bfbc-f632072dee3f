#!/usr/bin/env python3
"""
测试Graph-DiT模型的采样能力
"""

import torch
import numpy as np
from rdkit import Chem
import sys
import os

# 添加路径
sys.path.append('graph_dit')

def test_atom_decoder():
    """测试原子解码器"""
    print("=== 测试原子解码器 ===")
    
    # 模拟原子解码器
    atom_decoder = {0: 'C', 1: 'N', 2: 'O', 3: 'F', 4: 'S', 5: 'Cl', 6: 'Br', 7: 'I'}
    
    print(f"原子解码器: {atom_decoder}")
    
    # 测试所有索引
    for i in range(8):
        if i in atom_decoder:
            print(f"索引 {i} -> {atom_decoder[i]}")
        else:
            print(f"错误: 索引 {i} 不在解码器中!")
    
    return atom_decoder

def create_simple_molecule(atom_decoder):
    """创建一个简单的测试分子"""
    print("\n=== 创建简单测试分子 ===")
    
    try:
        # 创建一个简单的甲烷分子 (CH4)
        mol = Chem.RWMol()
        
        # 添加碳原子
        carbon_idx = mol.AddAtom(Chem.Atom('C'))
        print(f"添加碳原子，索引: {carbon_idx}")
        
        # 尝试转换为SMILES
        mol_sanitized = mol.GetMol()
        smiles = Chem.MolToSmiles(mol_sanitized)
        print(f"生成的SMILES: {smiles}")
        
        return smiles
        
    except Exception as e:
        print(f"创建分子时出错: {e}")
        return None

def test_rdkit_basic():
    """测试RDKit基本功能"""
    print("\n=== 测试RDKit基本功能 ===")
    
    # 测试一些简单的SMILES
    test_smiles = ['C', 'CC', 'CCO', 'c1ccccc1']
    
    for smiles in test_smiles:
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is not None:
                canonical_smiles = Chem.MolToSmiles(mol)
                print(f"✓ {smiles} -> {canonical_smiles}")
            else:
                print(f"✗ {smiles} -> 无效")
        except Exception as e:
            print(f"✗ {smiles} -> 错误: {e}")

def main():
    print("Graph-DiT 采样测试")
    print("=" * 50)
    
    # 测试原子解码器
    atom_decoder = test_atom_decoder()
    
    # 测试RDKit基本功能
    test_rdkit_basic()
    
    # 创建简单分子
    simple_mol = create_simple_molecule(atom_decoder)
    
    print("\n=== 总结 ===")
    if simple_mol:
        print("✓ 原子解码器和RDKit功能正常")
        print("问题可能在于:")
        print("1. 模型还没有充分训练")
        print("2. 生成的分子结构无效")
        print("3. 需要更强的化学约束")
    else:
        print("✗ 基本功能有问题")
    
    print("\n建议:")
    print("1. 继续训练到200-300轮")
    print("2. 检查化学约束设置")
    print("3. 考虑调整训练参数")

if __name__ == "__main__":
    main()
