#!/usr/bin/env python3
"""
清理数据集的脚本
修复SMILES字符串的化学错误，过滤无效分子
"""

import pandas as pd
import numpy as np
from rdkit import Chem
from rdkit.Chem import Descriptors, rdMolDescriptors
from tqdm import tqdm
import os

def clean_smiles(smiles, debug=False):
    """
    清理SMILES字符串，修复常见的化学错误
    """
    try:
        # 清理字符串
        smiles = str(smiles).strip()

        # 尝试解析分子
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            if debug:
                print(f"Failed to parse: {smiles}")
            return None, "Failed to parse"

        # 尝试清理分子
        try:
            Chem.SanitizeMol(mol)
        except Exception as e:
            if debug:
                print(f"Sanitization failed for {smiles}: {e}")
            return None, f"Sanitization failed: {str(e)}"

        # 重新生成标准SMILES
        clean_smiles = Chem.MolToSmiles(mol)
        return clean_smiles, "Success"

    except Exception as e:
        if debug:
            print(f"Exception for {smiles}: {e}")
        return None, f"Exception: {str(e)}"

def validate_molecule(smiles):
    """
    验证分子的化学有效性
    """
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return False, "Failed to parse SMILES"
        
        # 检查原子价
        try:
            Chem.SanitizeMol(mol)
        except Exception as e:
            return False, f"Sanitization failed: {str(e)}"
        
        # 检查分子大小
        num_atoms = mol.GetNumAtoms()
        if num_atoms < 5 or num_atoms > 50:
            return False, f"Molecule too small/large: {num_atoms} atoms"
        
        # 检查分子量
        mw = Descriptors.MolWt(mol)
        if mw < 100 or mw > 800:
            return False, f"Molecular weight out of range: {mw:.2f}"
        
        return True, "Valid"
        
    except Exception as e:
        return False, f"Validation error: {str(e)}"

def clean_dataset(input_file, output_file):
    """
    清理整个数据集
    """
    print(f"读取数据集: {input_file}")
    df = pd.read_csv(input_file)
    
    print(f"原始数据集大小: {len(df)} 个分子")
    
    # 检查SMILES列
    smiles_col = None
    for col in ['SMILES', 'smiles', 'Smiles']:
        if col in df.columns:
            smiles_col = col
            break
    
    if smiles_col is None:
        print("错误: 找不到SMILES列")
        return False
    
    print(f"使用SMILES列: {smiles_col}")
    
    # 清理和验证分子
    valid_indices = []
    cleaned_smiles = []
    invalid_reasons = []

    print("清理和验证分子...")

    # 先测试前几个分子
    print("测试前5个分子:")
    for i in range(min(5, len(df))):
        smiles = df[smiles_col].iloc[i]
        print(f"  {i}: {smiles}")
        clean_smi, reason = clean_smiles(smiles, debug=True)
        print(f"     结果: {clean_smi}, 原因: {reason}")

    for i, smiles in enumerate(tqdm(df[smiles_col], desc="Processing")):
        if pd.isna(smiles):
            invalid_reasons.append("Missing SMILES")
            continue

        # 清理SMILES
        clean_smi, clean_reason = clean_smiles(smiles)
        if clean_smi is None:
            invalid_reasons.append(clean_reason)
            continue

        # 验证分子
        is_valid, reason = validate_molecule(clean_smi)
        if is_valid:
            valid_indices.append(i)
            cleaned_smiles.append(clean_smi)
        else:
            invalid_reasons.append(reason)
    
    print(f"\n清理结果:")
    print(f"有效分子数: {len(valid_indices)}")
    print(f"无效分子数: {len(df) - len(valid_indices)}")
    print(f"有效率: {len(valid_indices)/len(df)*100:.2f}%")
    
    # 统计无效原因
    from collections import Counter
    reason_counts = Counter(invalid_reasons)
    print(f"\n无效分子原因统计:")
    for reason, count in reason_counts.most_common():
        print(f"  {reason}: {count}")
    
    if len(valid_indices) < 100:
        print("警告: 有效分子数量太少，可能需要使用其他数据集")
        return False
    
    # 创建清理后的数据集
    clean_df = df.iloc[valid_indices].copy()
    clean_df[smiles_col] = cleaned_smiles
    
    # 重新计算一些描述符（如果需要）
    print("重新计算分子描述符...")
    sas_scores = []
    logp_scores = []
    
    for smiles in tqdm(cleaned_smiles, desc="Computing descriptors"):
        mol = Chem.MolFromSmiles(smiles)
        
        # 计算SAS分数（如果有rdkit-pypi）
        try:
            from rdkit.Chem import rdMolDescriptors
            sas = rdMolDescriptors.BertzCT(mol) / 100.0  # 简化的SAS近似
        except:
            sas = 3.0  # 默认值
        sas_scores.append(sas)
        
        # 计算LogP
        logp = Descriptors.MolLogP(mol)
        logp_scores.append(logp)
    
    # 更新或添加描述符列
    if 'SA' not in clean_df.columns:
        clean_df['SA'] = sas_scores
    if 'LogP' not in clean_df.columns:
        clean_df['LogP'] = logp_scores
    
    # 保存清理后的数据集
    print(f"保存清理后的数据集到: {output_file}")
    clean_df.to_csv(output_file, index=False)
    
    # 保存统计信息
    stats = {
        'original_size': len(df),
        'cleaned_size': len(clean_df),
        'validity_rate': len(clean_df) / len(df),
        'invalid_reasons': dict(reason_counts)
    }
    
    stats_file = output_file.replace('.csv', '_stats.json')
    import json
    with open(stats_file, 'w') as f:
        json.dump(stats, f, indent=2)
    
    print(f"统计信息保存到: {stats_file}")
    return True

def main():
    """主函数"""
    input_file = "data/raw/fgfr1_with_toxic_solubility.csv"
    output_file = "data/raw/fgfr1_with_toxic_solubility_cleaned.csv"
    
    print("=" * 60)
    print("Graph-DiT 数据集清理工具")
    print("=" * 60)
    
    if not os.path.exists(input_file):
        print(f"错误: 找不到输入文件 {input_file}")
        return
    
    # 备份原文件
    backup_file = input_file + ".backup_original"
    if not os.path.exists(backup_file):
        print(f"备份原文件到: {backup_file}")
        import shutil
        shutil.copy2(input_file, backup_file)
    
    success = clean_dataset(input_file, output_file)
    
    if success:
        print("\n🎉 数据集清理完成!")
        print("\n建议步骤:")
        print(f"1. 检查清理后的数据集: {output_file}")
        print("2. 如果满意，替换原数据集:")
        print(f"   mv {output_file} {input_file}")
        print("3. 删除处理缓存: rm -rf data/processed/")
        print("4. 重新生成元信息: python comprehensive_fix.py")
        print("5. 重新开始训练")
    else:
        print("\n❌ 数据集清理失败")

if __name__ == "__main__":
    main()
