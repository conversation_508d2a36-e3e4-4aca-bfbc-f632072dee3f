#!/usr/bin/env python3
"""
测试分子生成质量的脚本
用于验证修复后的模型是否能生成有效分子
"""

import os
import sys
import json
import numpy as np

def analyze_transition_matrix(meta_file):
    """分析转移矩阵的质量"""
    print("=" * 50)
    print("转移矩阵分析")
    print("=" * 50)
    
    with open(meta_file, 'r') as f:
        meta_dict = json.load(f)
    
    transition_E = np.array(meta_dict['transition_E'])
    active_atoms = meta_dict['active_atoms']
    
    print(f"活跃原子: {active_atoms}")
    print(f"转移矩阵形状: {transition_E.shape}")
    
    # 分析关键原子对的转移概率
    atom_numbers = {'C': 6, 'N': 7, 'O': 8, 'F': 9, 'P': 15, 'S': 16, 'Cl': 17, 'Br': 35, 'I': 53}
    
    print("\n关键原子对的键形成概率:")
    important_pairs = [('C', 'C'), ('C', 'N'), ('C', 'O'), ('C', 'F'), ('N', 'O')]
    
    for atom1, atom2 in important_pairs:
        if atom1 in active_atoms and atom2 in active_atoms:
            i = atom_numbers[atom1] - 2
            j = atom_numbers[atom2] - 2
            
            probs = transition_E[i][j]
            bond_prob = sum(probs[1:])  # 除了无键之外的概率
            
            print(f"{atom1}-{atom2}: 无键={probs[0]:.3f}, 有键={bond_prob:.3f}")
            if len(probs) > 1:
                print(f"    单键={probs[1]:.3f}, 双键={probs[2]:.3f}, 三键={probs[3]:.3f}, 芳香={probs[4]:.3f}")
    
    # 统计整体情况
    total_pairs = 0
    reasonable_pairs = 0
    
    for i in range(len(transition_E)):
        for j in range(len(transition_E[i])):
            total_pairs += 1
            bond_prob = sum(transition_E[i][j][1:])
            if bond_prob > 0.1:  # 有合理的键形成概率
                reasonable_pairs += 1
    
    print(f"\n整体统计:")
    print(f"总原子对数: {total_pairs}")
    print(f"有合理键概率(>10%)的对数: {reasonable_pairs}")
    print(f"比例: {reasonable_pairs/total_pairs*100:.2f}%")

def check_dataset_stats(meta_file):
    """检查数据集统计信息"""
    print("\n" + "=" * 50)
    print("数据集统计信息")
    print("=" * 50)
    
    with open(meta_file, 'r') as f:
        meta_dict = json.load(f)
    
    print(f"数据集: {meta_dict['source']}")
    print(f"分子数量: {meta_dict['num_graph']}")
    print(f"最大原子数: {meta_dict['max_node']}")
    print(f"最大键数: {meta_dict['max_bond']}")
    
    # 原子类型分布
    atom_dist = meta_dict['atom_type_dist']
    active_atoms = meta_dict['active_atoms']
    
    print(f"\n原子类型分布:")
    for i, atom in enumerate(active_atoms):
        if i < len(atom_dist):
            print(f"  {atom}: {atom_dist[i]:.4f}")
    
    # 键类型分布
    bond_dist = meta_dict['bond_type_dist']
    bond_names = ['无键', '单键', '双键', '三键', '芳香键']
    
    print(f"\n键类型分布:")
    total_bonds = sum(bond_dist)
    for i, (name, count) in enumerate(zip(bond_names, bond_dist)):
        percentage = count / total_bonds * 100 if total_bonds > 0 else 0
        print(f"  {name}: {count} ({percentage:.2f}%)")

def generate_recommendations():
    """生成训练建议"""
    print("\n" + "=" * 50)
    print("训练建议")
    print("=" * 50)
    
    recommendations = [
        "1. 使用修复后的转移矩阵重新训练模型",
        "2. 增加化学约束权重以提高分子有效性",
        "3. 监控训练过程中的分子有效性指标",
        "4. 在验证阶段检查生成分子的化学合理性",
        "5. 如果仍有问题，考虑进一步调整转移矩阵",
        "6. 使用较小的学习率以确保稳定训练",
        "7. 定期保存检查点以便回滚"
    ]
    
    for rec in recommendations:
        print(rec)

def main():
    """主函数"""
    meta_file = "data/raw/fgfr1_with_toxic_solubility.meta.json"
    
    if not os.path.exists(meta_file):
        print(f"错误: 找不到元信息文件 {meta_file}")
        return
    
    print("Graph-DiT 分子生成质量测试")
    
    try:
        # 分析转移矩阵
        analyze_transition_matrix(meta_file)
        
        # 检查数据集统计
        check_dataset_stats(meta_file)
        
        # 生成建议
        generate_recommendations()
        
        print("\n" + "=" * 50)
        print("测试完成!")
        print("=" * 50)
        
        print("\n下一步:")
        print("1. 运行训练命令开始训练")
        print("2. 监控训练日志中的分子有效性指标")
        print("3. 在验证阶段检查生成的分子样本")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
